#!/usr/bin/env python3
"""
Network connection verification for privacy-modified Dyad
Monitors network connections to ensure only allowed AI providers are contacted
"""

import subprocess
import sys
import time
import re
from datetime import datetime

# Allowed domains for your chosen AI providers
ALLOWED_DOMAINS = [
    'api.openai.com',
    'api.anthropic.com', 
    'openrouter.ai',
    'generativelanguage.googleapis.com',
    'localhost',
    '127.0.0.1'
]

# Blocked domains that should never be contacted
BLOCKED_DOMAINS = [
    'us.i.posthog.com',
    'llm-gateway.dyad.sh',
    'engine.dyad.sh', 
    'supabase-oauth.dyad.sh',
    'api.github.com',
    'dyad.sh',
    'www.dyad.sh'
]

def log_message(message, level="INFO"):
    """Log a message with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [{level}] {message}")

def check_network_connections():
    """Monitor network connections and verify privacy"""
    log_message("🔍 Starting network connection monitoring...")
    log_message("✅ Allowed domains: " + ', '.join(ALLOWED_DOMAINS))
    log_message("❌ Blocked domains: " + ', '.join(BLOCKED_DOMAINS))
    log_message("\n📋 Instructions:")
    log_message("1. Start Dyad application")
    log_message("2. Use the AI chat features")
    log_message("3. Watch for any blocked domain connections")
    log_message("4. Press Ctrl+C to stop monitoring\n")
    
    try:
        while True:
            # Check for active network connections
            # This is a basic implementation - you might want to use more sophisticated tools
            try:
                if sys.platform == "win32":
                    # Windows netstat command
                    result = subprocess.run(['netstat', '-an'], 
                                          capture_output=True, text=True, timeout=5)
                    connections = result.stdout
                    
                    # Look for established connections
                    for line in connections.split('\n'):
                        if 'ESTABLISHED' in line:
                            # Extract IP addresses and ports
                            parts = line.split()
                            if len(parts) >= 3:
                                foreign_addr = parts[2]
                                # Basic check for suspicious connections
                                for blocked in BLOCKED_DOMAINS:
                                    if blocked in line.lower():
                                        log_message(f"⚠️  BLOCKED DOMAIN DETECTED: {foreign_addr}", "WARNING")
                
                elif sys.platform.startswith("linux"):
                    # Linux netstat command
                    result = subprocess.run(['netstat', '-tuln'], 
                                          capture_output=True, text=True, timeout=5)
                    # Similar processing for Linux
                    
                else:
                    log_message("📊 Platform-specific monitoring not implemented. Use manual network monitoring tools.", "INFO")
                    
            except subprocess.TimeoutExpired:
                log_message("Network check timed out", "WARNING")
            except Exception as e:
                log_message(f"Error checking connections: {e}", "ERROR")
            
            time.sleep(5)  # Check every 5 seconds
            
    except KeyboardInterrupt:
        log_message("\n✅ Monitoring stopped by user.")
        log_message("🔒 Privacy verification complete!")

def verify_dyad_executable():
    """Verify the Dyad executable exists"""
    import os
    
    exe_path = "out/dyad-win32-x64/dyad.exe"
    if os.path.exists(exe_path):
        log_message(f"✅ Dyad executable found: {exe_path}")
        return True
    else:
        log_message(f"❌ Dyad executable not found: {exe_path}")
        log_message("Run 'npm run package' first to build the application")
        return False

def main():
    """Main function"""
    log_message("🔒 Dyad Privacy Network Verification Tool")
    log_message("=" * 50)
    
    # Check if Dyad is built
    if not verify_dyad_executable():
        return
    
    log_message("🚀 Ready to monitor network connections!")
    log_message("Start your Dyad application now and begin using AI features.")
    
    try:
        input("Press Enter when ready to start monitoring...")
    except KeyboardInterrupt:
        log_message("Monitoring cancelled.")
        return
    
    check_network_connections()

if __name__ == "__main__":
    main()
