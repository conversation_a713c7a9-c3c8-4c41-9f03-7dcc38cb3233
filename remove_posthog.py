#!/usr/bin/env python3
"""
Script to remove all PostHog telemetry references from Dyad codebase
"""

import os
import re
import glob

def remove_posthog_imports_and_usage(file_path):
    """Remove PostHog imports and usage from a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Remove PostHog imports
        content = re.sub(r'import.*posthog.*from.*["\']posthog-js/react["\'];?\n?', '', content)
        content = re.sub(r'import.*usePostHog.*from.*["\']posthog-js/react["\'];?\n?', '', content)
        content = re.sub(r'import.*PostHog.*from.*["\']posthog-js/react["\'];?\n?', '', content)
        
        # Remove PostHog usage
        content = re.sub(r'const\s+posthog\s*=\s*usePostHog\(\);?\n?', '', content)
        content = re.sub(r'posthog\.capture\([^)]*\);?\n?', '// PostHog telemetry removed\n', content)
        content = re.sub(r'posthog\.captureException\([^)]*\);?\n?', '// PostHog telemetry removed\n', content)
        
        # Clean up multiple newlines
        content = re.sub(r'\n\n\n+', '\n\n', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Updated: {file_path}")
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")

def main():
    """Main function to process all TypeScript/JavaScript files"""
    
    # Find all TypeScript and JavaScript files
    patterns = [
        'src/**/*.ts',
        'src/**/*.tsx', 
        'src/**/*.js',
        'src/**/*.jsx'
    ]
    
    files_to_process = []
    for pattern in patterns:
        files_to_process.extend(glob.glob(pattern, recursive=True))
    
    print(f"Processing {len(files_to_process)} files...")
    
    for file_path in files_to_process:
        remove_posthog_imports_and_usage(file_path)
    
    print("PostHog removal complete!")

if __name__ == "__main__":
    main()
