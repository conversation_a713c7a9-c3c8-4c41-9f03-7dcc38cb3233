{"$schema": "https://biomejs.dev/schemas/1.4.1/schema.json", "vcs": {"useIgnoreFile": true}, "files": {"ignore": ["**/static/build/**", "**/third_party/**", "third_party/**", "**/external/**", "tools/**", "mesop/**/*.json", "scripts/**", "**/esbuild*", "**/venv/**"]}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedImports": "error"}, "complexity": {"useLiteralKeys": "off", "noForEach": "off"}, "style": {"noParameterAssign": "off", "noNonNullAssertion": "off"}, "suspicious": {"noExplicitAny": "off", "noFallthroughSwitchClause": "off", "noPrototypeBuiltins": "off", "noArrayIndexKey": "off"}, "a11y": {"useButtonType": "off", "useIframeTitle": "off", "noLabelWithoutControl": "off", "noSvgWithoutTitle": "off"}}}, "formatter": {"enabled": false}, "javascript": {"parser": {"unsafeParameterDecoratorsEnabled": true}}}