{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2023"],
    "module": "ESNext",
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "noImplicitAny": true,
    "sourceMap": true,
    "baseUrl": ".",
    "outDir": "dist",
    "moduleResolution": "bundler",
    "resolveJsonModule": true

    // /* Bundler mode */
    // // "moduleResolution": "bundler",
    // "allowImportingTsExtensions": true,
    // "isolatedModules": true,
    // "moduleDetection": "force",
    // "noEmit": true,
    // "jsx": "react-jsx",

    // /* Linting */
    // "strict": true,
    // "noUnusedLocals": true,
    // "noUnusedParameters": true,
    // "noFallthroughCasesInSwitch": true,
    // "noUncheckedSideEffectImports": true
  },
  "include": ["src"]
}
