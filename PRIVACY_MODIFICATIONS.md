# Dyad Privacy Modifications Guide

This document outlines all modifications made to ensure <PERSON><PERSON><PERSON> only communicates with your chosen AI providers (OpenAI, Anthropic, OpenRouter, Google Gemini) and removes all other third-party integrations.

## ✅ Completed Modifications

### 1. **PostHog Telemetry Removal** (CRITICAL)
- **Files Modified:**
  - `src/renderer.tsx` - Removed PostHog initialization and provider
  - `src/hooks/useSettings.ts` - Removed PostHog usage
  - `src/components/ErrorBoundary.tsx` - Removed error tracking
  - `src/pages/home.tsx` - Removed PostHog hooks
  - `package.json` - Removed `posthog-js` dependency

### 2. **Auto-Update Mechanism Disabled**
- **Files Modified:**
  - `src/main.ts` - Removed `updateElectronApp()` call
  - `package.json` - Removed `update-electron-app` dependency

### 3. **Release Notes Fetching Disabled**
- **Files Modified:**
  - `src/ipc/handlers/release_note_handlers.ts` - Always returns `exists: false`

### 4. **Third-Party Service Integrations Disabled**
- **GitHub Integration:** `src/ipc/ipc_host.ts` - Commented out `registerGithubHandlers()`
- **Supabase Integration:** `src/ipc/ipc_host.ts` - Commented out `registerSupabaseHandlers()`
- **Dyad Pro Integration:** `src/ipc/ipc_host.ts` - Commented out `registerProHandlers()`

### 5. **IPC Channels Cleaned Up**
- **File Modified:** `src/preload.ts`
- **Disabled Channels:**
  - All GitHub-related channels
  - All Supabase-related channels  
  - Dyad Pro budget channel

## 🔧 Additional Modifications Needed

### 6. **Remove Remaining PostHog References**
Run the provided Python script to clean up any remaining PostHog references:

```bash
python remove_posthog.py
```

### 7. **Clean Package Dependencies**
Remove unused dependencies after modifications:

```bash
npm uninstall posthog-js update-electron-app
npm install  # Reinstall to clean up package-lock.json
```

### 8. **Remove Telemetry UI Components**
- Delete or modify `src/components/TelemetryBanner.tsx`
- Delete or modify `src/components/TelemetrySwitch.tsx`
- Remove telemetry settings from settings pages

### 9. **Remove Deep Link Handlers**
In `src/main.ts`, remove or disable:
- `handleSupabaseOAuthReturn`
- `handleDyadProReturn`
- Related deep link processing

### 10. **Clean Up Database Schema**
Remove Supabase and GitHub related fields from:
- `src/db/schema.ts` - Remove `supabaseProjectId`, `githubOrg`, `githubRepo`, `githubBranch`

## 🎯 AI Provider Configuration

### Allowed AI Providers (Keep These)
1. **OpenAI** - `openai` provider
2. **Anthropic** - `anthropic` provider  
3. **OpenRouter** - `openrouter` provider
4. **Google Gemini** - `google` provider

### Configuration Files
- `src/ipc/shared/language_model_helpers.ts` - Contains provider configurations
- Environment variables: `OPENAI_API_KEY`, `ANTHROPIC_API_KEY`, `GEMINI_API_KEY`, `OPENROUTER_API_KEY`

## 🚫 Network Connections After Modifications

### ✅ ALLOWED (AI Providers Only)
- `https://api.openai.com` - OpenAI API
- `https://api.anthropic.com` - Anthropic API
- `https://openrouter.ai` - OpenRouter API
- `https://generativelanguage.googleapis.com` - Google Gemini API

### ❌ BLOCKED (Privacy)
- `https://us.i.posthog.com` - PostHog telemetry
- `https://llm-gateway.dyad.sh` - Dyad Pro services
- `https://engine.dyad.sh` - Dyad Pro engine
- `https://supabase-oauth.dyad.sh` - Supabase OAuth
- `https://api.github.com` - GitHub API
- `https://www.dyad.sh` - Release notes
- Auto-update servers

## 🔒 Security Recommendations

1. **Firewall Rules:** Block outbound connections except to allowed AI provider domains
2. **Network Monitoring:** Monitor network traffic to verify no unexpected connections
3. **Regular Audits:** Periodically check for new third-party integrations in updates
4. **Environment Variables:** Use environment variables for API keys instead of storing in app

## 🧪 Testing Your Modifications

1. **Build the app:** `npm run make`
2. **Network monitoring:** Use tools like Wireshark or Little Snitch to monitor connections
3. **Verify functionality:** Test AI chat functionality with your providers
4. **Check logs:** Ensure no errors from disabled services

## 📝 Notes

- Keep the local model handlers (`registerLocalModelHandlers`) for Ollama/LM Studio support
- The proxy server (`worker/proxy_server.js`) is kept for local development server proxying
- Database functionality remains local (SQLite)
- File system operations remain local

## 🔄 Future Updates

When updating Dyad:
1. Review new dependencies in `package.json`
2. Check for new third-party service integrations
3. Re-apply these privacy modifications
4. Test network connections again

---

**Result:** A fully private Dyad installation that only communicates with your chosen AI providers.
