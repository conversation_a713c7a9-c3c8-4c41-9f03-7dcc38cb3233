import log from "electron-log";
import fetch from "node-fetch";
import { create<PERSON><PERSON><PERSON><PERSON><PERSON> } from "./safe_handle";
import { DoesReleaseNoteExistParams } from "../ipc_types";
import { IS_TEST_BUILD } from "../utils/test_utils";

const logger = log.scope("release_note_handlers");

const handle = createLoggedHandler(logger);

export function registerReleaseNoteHandlers() {
  handle(
    "does-release-note-exist",
    async (_, params: DoesReleaseNoteExistParams) => {
      // Release notes disabled for privacy - no external network calls
      return { exists: false };
    },
  );

  logger.debug("Registered release note IPC handlers");
}
